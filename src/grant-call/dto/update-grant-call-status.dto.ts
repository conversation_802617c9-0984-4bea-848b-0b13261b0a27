import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString, MaxLength, IsIn } from 'class-validator';
import { WorkflowStatus } from '../../workflow/enums/workflow-status.enum';

export class UpdateGrantCallStatusDto {
  @ApiProperty({ 
    enum: [WorkflowStatus.CLOSED], 
    enumName: 'GrantCallWorkflowStatus',
    description: 'Status to set for the Grant Call. Currently only CLOSED is supported.'
  })
  @IsNotEmpty()
  @IsEnum(WorkflowStatus)
  @IsIn([WorkflowStatus.CLOSED], {
    message: `Grant Call status can only be set to '${WorkflowStatus.CLOSED}'. Other status changes should use transition endpoints.`,
  })
  status: WorkflowStatus.CLOSED;

  @ApiProperty({ description: 'Reason for closing the Grant Call', type: String })
  @IsNotEmpty()
  @IsString()
  @MaxLength(1000)
  reason: string;
}
