import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { GrantCall } from './entities/grant-call.entity';
import { GrantCallStageSetting } from './entities/grant-call-stage-setting.entity';
import { GrantDistributionRule } from './entities/grant-distribution-rule.entity';
import { GrantApplication } from '../grant-application/entities/grant-application.entity';
import { FindOneGrantCallBaseDto } from './dto';
import { GrantCategory } from './enums/grant-category.enum';
import { StageCode } from '../workflow/enums/stage-code.enum';
import {
  GrantCallTimingSettings,
  GrantCallUrlDtoKey,
  REQUIRED_TIMING_STAGES,
  STAGE_URL_DTO_KEYS,
} from './grant-call.constants';
import { StageTransitionType } from '../workflow/enums/stage-transition-type.enum';

@Injectable()
export class GrantCallMapper {
  constructor(
    @InjectRepository(GrantApplication)
    private readonly grantApplicationRepository: Repository<GrantApplication>,
  ) {}

  async mapGrantCallToDetailDto(grantCall: GrantCall): Promise<FindOneGrantCallBaseDto> {
    const grantApplicationsCount = await this.grantApplicationRepository.count({
      where: { grantCall: { id: grantCall.id } },
    });

    const {
      grantCallSlug,
      name,
      description,
      businessCategory,
      categories,
      totalGrantAmount,
      createdBy,
      stageSettings,
      distributionRules,
      workflowState,
      updatedAt,
    } = grantCall;

    const { id, displayName } = createdBy;
    const timingSettings = this.extractTimingsFromSettings(stageSettings);
    const urlSettings = this.extractUrlsFromSettings(stageSettings);
    const grantDistributionSettings = this.extractGrantDistributionSettings(distributionRules);

    const isAbleToChangeStageManually =
      grantCall.workflowState.currentStepDefinition.transitionType === StageTransitionType.MANUAL &&
      !grantCall.workflowState.currentStepDefinition.isTerminal &&
      (!grantCall.workflowState.currentStepEndsAt || grantCall.workflowState.currentStepEndsAt <= new Date()) &&
      grantCall.workflowState.workflowTemplate.steps.some(
        (step) => step.sequenceNumber > grantCall.workflowState.currentStepDefinition.sequenceNumber,
      );

    return {
      grantCallSlug,
      name,
      description,
      businessCategory,
      totalGrantAmount,
      updatedAt,
      ...timingSettings,
      ...urlSettings,
      createdBy: { id, displayName },
      categories: categories as GrantCategory[],
      grantDistribution: grantDistributionSettings,
      grantApplicationsCount,
      status: workflowState?.currentStepDefinition.code ?? StageCode.GC_CLOSED,
      isAbleToChangeStageManually,
    };
  }

  private findSettingByCode(settings: GrantCallStageSetting[], code: StageCode): GrantCallStageSetting {
    return settings.find(({ workflowStepDefinition }) => workflowStepDefinition.code === code);
  }

  private extractTimingsFromSettings(settings: GrantCallStageSetting[]): GrantCallTimingSettings {
    const relevantSettings = REQUIRED_TIMING_STAGES.map((code) => this.findSettingByCode(settings, code));
    const [applicationSetting, voting1Setting, voting2Setting] = relevantSettings;

    return {
      openForApplicationStart: applicationSetting?.startDate.toISOString() ?? null,
      openForApplicationEnd: applicationSetting?.endDate.toISOString() ?? null,
      communityVotingTime1: voting1Setting?.durationSeconds ?? null,
      communityVotingTime2: voting2Setting?.durationSeconds ?? null,
    };
  }

  private extractGrantDistributionSettings(rules: GrantDistributionRule[]): number[] {
    return rules?.map(({ value }) => value ?? 0);
  }

  private extractUrlsFromSettings(
    settings: GrantCallStageSetting[],
  ): Pick<FindOneGrantCallBaseDto, GrantCallUrlDtoKey> {
    return Object.fromEntries(
      (Object.entries(STAGE_URL_DTO_KEYS) as [StageCode, GrantCallUrlDtoKey][]).map(([stageCode, dtoKey]) => [
        dtoKey,
        this.findSettingByCode(settings, stageCode)?.stageUrl ?? null,
      ]),
    ) as Record<GrantCallUrlDtoKey, string | null>;
  }
}
